// apps/mobile/app/subscription-cancelled.tsx
import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

export default function SubscriptionCancelledScreen() {
  const router = useRouter();

  useEffect(() => {
    // Auto-redirect after 5 seconds
    const timer = setTimeout(() => {
      router.replace('/(tabs)/');
    }, 5000);

    return () => clearTimeout(timer);
  }, [router]);

  const handleTryAgain = () => {
    router.back();
  };

  const handleGoHome = () => {
    router.replace('/(tabs)/');
  };

  return (
    <LinearGradient
      colors={['#EF4444', '#DC2626']}
      className="flex-1 justify-center items-center px-6"
    >
      <View className="bg-white rounded-2xl p-8 w-full max-w-sm items-center shadow-lg">
        {/* Cancel Icon */}
        <View className="w-20 h-20 bg-red-100 rounded-full items-center justify-center mb-6">
          <Text className="text-4xl">✕</Text>
        </View>

        {/* Cancel Message */}
        <Text className="text-2xl font-bold text-gray-900 text-center mb-4">
          Payment Cancelled
        </Text>
        
        <Text className="text-gray-600 text-center mb-8 leading-6">
          Your payment was cancelled. No charges were made to your account.
        </Text>

        {/* Action Buttons */}
        <View className="w-full space-y-3">
          <TouchableOpacity
            onPress={handleTryAgain}
            className="bg-blue-600 w-full py-4 rounded-xl items-center"
          >
            <Text className="text-white font-semibold text-lg">
              Try Again
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleGoHome}
            className="bg-gray-200 w-full py-4 rounded-xl items-center"
          >
            <Text className="text-gray-800 font-semibold text-lg">
              Go to Home
            </Text>
          </TouchableOpacity>
        </View>

        {/* Auto-redirect notice */}
        <Text className="text-gray-400 text-sm text-center mt-4">
          Redirecting to home in 5 seconds...
        </Text>
      </View>
    </LinearGradient>
  );
}
