import { createFileRoute, redirect } from '@tanstack/react-router'
import { DashboardShell } from '@/components/dashboard/dashboard-shell'
import { useSession } from '@/lib/auth-client'

export const Route = createFileRoute('/dashboard')({
  component: DashboardPage,
})

function DashboardPage() {
  const { data: session, isPending } = useSession()

  // Show loading state while session is being fetched
  if (isPending) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Redirect to login if no session
  if (!session?.user) {
    window.location.href = '/login'
    return null
  }

  return <DashboardShell user={session.user} />
}
