// convex/subscriptions.ts
import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const getUserSubscription = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!subscription) {
      return { hasActiveSubscription: false, subscription: null };
    }

    const isActive =
      subscription.status === "active" &&
      subscription.currentPeriodEnd > Date.now();

    return {
      hasActiveSubscription: isActive,
      subscription,
    };
  },
});

export const updateSubscription = mutation({
  args: {
    userId: v.string(),
    platform: v.union(v.literal("stripe"), v.literal("apple"), v.literal("google")),
    externalId: v.string(),
    productId: v.string(),
    status: v.union(
      v.literal("active"),
      v.literal("canceled"),
      v.literal("past_due"),
      v.literal("trialing"),
      v.literal("incomplete"),
      v.literal("incomplete_expired"),
      v.literal("unpaid")
    ),
    currentPeriodStart: v.number(),
    currentPeriodEnd: v.number(),
    cancelAtPeriodEnd: v.optional(v.boolean()),
    canceledAt: v.optional(v.number()),
    trialStart: v.optional(v.number()),
    trialEnd: v.optional(v.number()),
    metadata: v.optional(v.object({})),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    const subscriptionData = {
      userId: args.userId,
      platform: args.platform,
      externalId: args.externalId,
      productId: args.productId,
      status: args.status,
      currentPeriodStart: args.currentPeriodStart,
      currentPeriodEnd: args.currentPeriodEnd,
      cancelAtPeriodEnd: args.cancelAtPeriodEnd,
      canceledAt: args.canceledAt,
      trialStart: args.trialStart,
      trialEnd: args.trialEnd,
      metadata: args.metadata,
      updatedAt: Date.now(),
    };

    if (existing) {
      await ctx.db.patch(existing._id, subscriptionData);
      return existing._id;
    } else {
      return await ctx.db.insert("subscriptions", {
        ...subscriptionData,
        createdAt: Date.now(),
      });
    }
  },
});

export const checkFeatureAccess = query({
  args: {
    userId: v.string(),
    featureId: v.string(),
  },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    const hasActiveSubscription = subscription
      ? subscription.status === "active" && subscription.currentPeriodEnd > Date.now()
      : false;

    // Define your feature access rules based on subscription
    const featureRules: Record<string, boolean> = {
      "premium_ai": hasActiveSubscription,
      "unlimited_messages": hasActiveSubscription,
      "advanced_analytics": hasActiveSubscription,
      "export_data": hasActiveSubscription,
    };

    return {
      allowed: featureRules[args.featureId] || false,
      hasSubscription: hasActiveSubscription,
    };
  },
});