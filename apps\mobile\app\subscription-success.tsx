// apps/mobile/app/subscription-success.tsx
import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

export default function SubscriptionSuccessScreen() {
  const router = useRouter();

  useEffect(() => {
    // Auto-redirect after 3 seconds
    const timer = setTimeout(() => {
      router.replace('/(tabs)/');
    }, 3000);

    return () => clearTimeout(timer);
  }, [router]);

  const handleContinue = () => {
    router.replace('/(tabs)/');
  };

  return (
    <LinearGradient
      colors={['#4F46E5', '#7C3AED']}
      className="flex-1 justify-center items-center px-6"
    >
      <View className="bg-white rounded-2xl p-8 w-full max-w-sm items-center shadow-lg">
        {/* Success Icon */}
        <View className="w-20 h-20 bg-green-100 rounded-full items-center justify-center mb-6">
          <Text className="text-4xl">✓</Text>
        </View>

        {/* Success Message */}
        <Text className="text-2xl font-bold text-gray-900 text-center mb-4">
          Payment Successful!
        </Text>
        
        <Text className="text-gray-600 text-center mb-8 leading-6">
          Your subscription has been activated. You now have access to all premium features.
        </Text>

        {/* Continue Button */}
        <TouchableOpacity
          onPress={handleContinue}
          className="bg-blue-600 w-full py-4 rounded-xl items-center"
        >
          <Text className="text-white font-semibold text-lg">
            Continue to App
          </Text>
        </TouchableOpacity>

        {/* Auto-redirect notice */}
        <Text className="text-gray-400 text-sm text-center mt-4">
          Redirecting automatically in 3 seconds...
        </Text>
      </View>
    </LinearGradient>
  );
}
