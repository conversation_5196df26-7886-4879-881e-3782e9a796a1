// convex/billing.ts
import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { v4 as uuidv4 } from "uuid";

export const getProducts = query({
  handler: async (ctx) => {
    return await ctx.db
      .query("products")
      .filter((q) => q.eq(q.field("active"), true))
      .collect();
  },
});

export const createCheckoutSession = mutation({
  args: {
    userId: v.string(),
    productId: v.string(),
    customerEmail: v.string(),
    successUrl: v.string(),
    cancelUrl: v.string(),
    platform: v.union(v.literal("ios"), v.literal("android"), v.literal("web")),
  },
  handler: async (ctx, args) => {
    const sessionId = uuidv4();
    const expiresAt = Date.now() + (30 * 60 * 1000); // 30 minutes

    await ctx.db.insert("checkout_sessions", {
      sessionId,
      userId: args.userId,
      productId: args.productId,
      customerEmail: args.customerEmail,
      successUrl: args.successUrl,
      cancelUrl: args.cancelUrl,
      platform: args.platform,
      status: "pending",
      expiresAt,
      createdAt: Date.now(),
    });

    // Return the URL to your web app's mobile checkout page
    return `${process.env.WEB_APP_URL}/mobile-checkout/${sessionId}`;
  },
});

export const getCheckoutSession = query({
  args: { sessionId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("checkout_sessions")
      .withIndex("by_session_id", (q) => q.eq("sessionId", args.sessionId))
      .first();
  },
});