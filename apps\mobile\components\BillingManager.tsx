// apps/mobile/components/BillingManager.tsx
import React from 'react';
import { Platform, Linking, Alert } from 'react-native';
import { View, Text, TouchableOpacity } from 'react-native';
import { useQuery, useMutation } from 'convex/react';
import { api } from '~/../../convex/_generated/api';

interface BillingManagerProps {
  userId: string;
  userEmail: string;
}

export const BillingManager = ({ userId, userEmail }: BillingManagerProps) => {
  // TODO: Add back once Convex types are generated
  // const subscription = useQuery(api.subscriptions.getUserSubscription, { userId });
  // const products = useQuery(api.billing.getProducts);
  // const createCheckoutSession = useMutation(api.billing.createCheckoutSession);

  // Placeholder data for now
  const subscription = null;
  const products: any[] = [];
  const createCheckoutSession = async (args: any) => {
    console.log('Creating checkout session:', args);
    return { checkoutUrl: 'https://example.com/checkout' };
  };

  const handleSubscriptionPurchase = async (productId: string) => {
    try {
      const platform = Platform.OS === 'ios' ? 'ios' : 'android';
      const appScheme = 'mobile'; // Use your app scheme from app.config.ts

      const result = await createCheckoutSession({
        userId,
        productId,
        customerEmail: userEmail,
        successUrl: `${appScheme}://subscription-success`,
        cancelUrl: `${appScheme}://subscription-cancelled`,
        platform,
      });

      const canOpen = await Linking.canOpenURL(result.checkoutUrl);
      if (canOpen) {
        await Linking.openURL(result.checkoutUrl);
      } else {
        Alert.alert('Error', 'Unable to open payment page');
      }
    } catch (error) {
      console.error('Checkout error:', error);
      Alert.alert('Error', 'Failed to initiate payment');
    }
  };

  const SubscriptionCard = ({ product }: { product: any }) => (
    <View className="bg-white rounded-lg p-4 mb-4 shadow-sm border border-gray-200">
      <Text className="text-xl font-bold text-gray-800 mb-2">
        {product.name}
      </Text>
      <Text className="text-gray-600 mb-3">
        {product.description}
      </Text>
      <Text className="text-2xl font-bold text-blue-600 mb-3">
        ${product.price}/{product.interval}
      </Text>
      <View className="mb-4">
        {product.features.map((feature: string, index: number) => (
          <Text key={index} className="text-gray-700 mb-1">
            ✓ {feature}
          </Text>
        ))}
      </View>
      <TouchableOpacity
        onPress={() => handleSubscriptionPurchase(product.id)}
        className="bg-blue-600 rounded-lg py-3 px-4"
      >
        <Text className="text-white text-center font-semibold">
          {subscription?.hasActiveSubscription ? 'Change Plan' : 'Subscribe'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View className="flex-1 bg-gray-50 p-4">
      <Text className="text-2xl font-bold text-gray-800 mb-6">
        Subscription Plans
      </Text>

      {subscription?.hasActiveSubscription && (
        <View className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <Text className="text-green-800 font-semibold mb-1">
            Active Subscription
          </Text>
          <Text className="text-green-700">
            Your subscription is active until{' '}
            {new Date(subscription.subscription.currentPeriodEnd).toLocaleDateString()}
          </Text>
        </View>
      )}

      {products?.map((product) => (
        <SubscriptionCard key={product.id} product={product} />
      ))}
    </View>
  );
};
