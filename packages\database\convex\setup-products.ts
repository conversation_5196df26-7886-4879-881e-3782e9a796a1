// packages/database/convex/setup-products.ts
import { mutation } from "./_generated/server";

// Run this mutation once to set up sample products
export const setupSampleProducts = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if products already exist
    const existingProducts = await ctx.db.query("products").collect();
    if (existingProducts.length > 0) {
      return { message: "Products already exist", count: existingProducts.length };
    }

    // Create sample products
    const products = [
      {
        id: "premium-monthly",
        name: "Premium Monthly",
        description: "Full access to all premium features with monthly billing",
        price: 9.99,
        currency: "USD",
        interval: "month" as const,
        features: [
          "Unlimited AI conversations",
          "Advanced analytics",
          "Priority support",
          "Export data",
          "Custom integrations"
        ],
        active: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: "premium-yearly",
        name: "Premium Yearly",
        description: "Full access to all premium features with yearly billing (2 months free)",
        price: 99.99,
        currency: "USD",
        interval: "year" as const,
        features: [
          "Unlimited AI conversations",
          "Advanced analytics",
          "Priority support",
          "Export data",
          "Custom integrations",
          "2 months free"
        ],
        active: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: "basic-monthly",
        name: "Basic Monthly",
        description: "Essential features for getting started",
        price: 4.99,
        currency: "USD",
        interval: "month" as const,
        features: [
          "100 AI conversations per month",
          "Basic analytics",
          "Email support",
          "Standard integrations"
        ],
        active: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      }
    ];

    const insertedIds: string[] = [];
    for (const product of products) {
      const id = await ctx.db.insert("products", product);
      insertedIds.push(id);
    }

    return {
      message: "Sample products created successfully",
      count: insertedIds.length,
      productIds: insertedIds
    };
  },
});
