// apps/web/app/routes/admin/billing.tsx - Admin billing management
import { createFileRoute, redirect } from '@tanstack/react-router'
import { useAutumn, useCustomer } from "autumn-js/react";
import { Button } from "~/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "~/components/ui/card";

export const Route = createFileRoute("/admin/billing")({
  component: AdminBillingPage,
});

function AdminBillingPage() {
  const { attach, check } = useAutumn();
  const { customer } = useCustomer();

  const handleUpgrade = async (productId: string) => {
    try {
      await attach({ productId });
    } catch (error) {
      console.error('Billing error:', error);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-8">Admin Billing</h1>
      
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Current Plan</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              {customer?.subscription ? 'Pro Plan' : 'Free Plan'}
            </p>
            <Button onClick={() => handleUpgrade('admin-pro')}>
              {customer?.subscription ? 'Manage Subscription' : 'Upgrade to Pro'}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Usage Analytics</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              View detailed usage analytics and billing information.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}