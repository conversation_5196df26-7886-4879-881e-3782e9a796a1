// apps/start-basic/src/routes/mobile-checkout.$sessionId.tsx 
import { createFileRoute, redirect } from '@tanstack/react-router'
import { useQuery } from "convex/react";
import { api } from "convex/_generated/api";
import { useAutumn } from "autumn-js/react";
import { useEffect, useState } from "react";

export const Route = createFileRoute("/mobile-checkout/$sessionId")({
  component: MobileCheckoutPage,
});

function MobileCheckoutPage() {
  const { sessionId } = Route.useParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const session = useQuery(api.billing.getCheckoutSession, { sessionId });
  const { attach } = useAutumn();

  useEffect(() => {
    if (session && session.status === "pending") {
      handleCheckout();
    } else if (session && session.status !== "pending") {
      setError("This checkout session has already been used or expired.");
      setLoading(false);
    }
  }, [session]);

  const handleCheckout = async () => {
    if (!session) return;

    try {
      // Use Autumn to create Stripe checkout
      await attach({
        productId: session.productId,
        customerId: session.userId,
        customerEmail: session.customerEmail,
        successUrl: session.successUrl,
        cancelUrl: session.cancelUrl,
        metadata: {
          sessionId: session.sessionId,
          platform: session.platform,
        },
      });
    } catch (err) {
      console.error("Checkout error:", err);
      setError("Failed to initialize checkout. Please try again.");
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center p-8 bg-white rounded-xl shadow-lg">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h1 className="text-2xl font-bold text-gray-800 mb-2">
            Setting up your subscription...
          </h1>
          <p className="text-gray-600">
            You'll be redirected to complete your payment.
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-pink-100">
        <div className="text-center p-8 bg-white rounded-xl shadow-lg max-w-md">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            Checkout Error
          </h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.close()}
            className="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return null;
}